// React 核心依赖导入
import { useState, useMemo, useEffect, useRef } from "react";
// Adobe React Spectrum UI 组件库导入
import {
	TreeView,
	TreeViewItem,
	TreeViewItemContent,
	Text,
} from "@adobe/react-spectrum";
// 导入类型定义
import type { Key, Selection } from "@adobe/react-spectrum";
// 导入图标
import Folder from "@spectrum-icons/workflow/Folder";
import FileTxt from "@spectrum-icons/workflow/FileTxt";
import DocumentFragment from "@spectrum-icons/workflow/DocumentFragment";
import DocumentFragmentGroup from "@spectrum-icons/workflow/DocumentFragmentGroup";
import ViewList from "@spectrum-icons/workflow/ViewList";
import Home from "@spectrum-icons/workflow/Home";
// 导入导航相关类型
import type { NavigationNode } from "../services/api/types";
// 导入组件专用样式
import "./NavigationTreeView.css";

// ==================== 图标映射系统 ====================

/**
 * 图标映射配置
 * 将图标名称映射到对应的 React Spectrum 图标组件
 */
const iconMapping = {
	"相册": DocumentFragmentGroup,
	"图像": DocumentFragment,
	"文件夹": Folder,
	"文件": FileTxt,
	"列表": ViewList,
	"首页": Home,
	// 添加更多映射关系
	"DocumentFragmentGroup": DocumentFragmentGroup,
	"DocumentFragment": DocumentFragment,
	"Folder": Folder,
	"FileTxt": FileTxt,
	"ViewList": ViewList,
	"Home": Home,
} as const;

/**
 * 获取图标组件
 * @param iconName 图标名称
 * @returns 对应的图标组件，如果未找到则返回默认图标
 */
function getIconComponent(iconName: string) {
	return iconMapping[iconName as keyof typeof iconMapping] || FileTxt;
}
/**
 * NavigationTreeView 组件的 Props 接口
 */
export interface NavigationTreeViewProps {
	/** 选择变化回调函数 */
	onSelectionChange?: (selectedKey: string | null) => void;
	/** 可选的自定义样式类 */
	className?: string;
	/** 可选的高度设置，默认为 "100%" */
	height?: string;
	/** 可选的最大宽度设置，默认为 "100%" */
	maxWidth?: string;
	/** 可选的 ARIA 标签，默认为 "导航菜单" */
	ariaLabel?: string;
	/** 动态导航数据 */
	navigationData?: NavigationNode[] | null;
	/** 导航数据加载状态 */
	loading?: boolean;
	/** 配置数据是否就绪 */
	configurationDataReady?: boolean;
}

/**
 * NavigationTreeView - 赛事导航树形菜单组件
 *
 * 功能特性：
 * - 父节点整个区域可点击展开/折叠
 * - 父节点不可选中，子节点可选中
 * - 单选模式和高亮样式
 * - 默认展开状态
 * - 支持滚动
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function NavigationTreeView({
	onSelectionChange,
	className,
	height = "100%",
	maxWidth = "100%",
	ariaLabel = "导航菜单",
	navigationData = null,
	loading = false,
	configurationDataReady = false,
}: NavigationTreeViewProps) {
	// 调试：组件接收到的props
	console.log('[NavigationTreeView] 🔍 组件props调试', {
		navigationDataExists: !!navigationData,
		navigationDataLength: navigationData?.length || 0,
		loading,
		configurationDataReady,
		navigationDataSample: navigationData?.slice(0, 3).map(n => ({ id: n.id, name: n.name, contentType: n.contentType })),
		timestamp: Date.now(),
		action: 'component_props_debug'
	});

	// 添加更详细的数据结构调试
	if (navigationData && navigationData.length > 0) {
		console.log('[NavigationTreeView] 📊 导航数据详细结构', {
			totalNodes: navigationData.length,
			parentNodes: navigationData.filter(n => n.children && n.children.length > 0).length,
			leafNodes: navigationData.filter(n => !n.children || n.children.length === 0).length,
			firstNode: navigationData[0],
			allNodeIds: navigationData.map(n => n.id),
			timestamp: Date.now(),
			action: 'navigation_data_structure_debug'
		});
	} else {
		console.log('[NavigationTreeView] ⚠️ 导航数据为空或未定义', {
			navigationData,
			navigationDataType: typeof navigationData,
			timestamp: Date.now(),
			action: 'navigation_data_empty_debug'
		});
	}

	// 使用 ref 存储最新的 onSelectionChange 函数，避免依赖数组问题
	const onSelectionChangeRef = useRef(onSelectionChange);
	onSelectionChangeRef.current = onSelectionChange;

	// 管理 TreeView 选择状态
	const [selectedTreeKeys, setSelectedTreeKeys] = useState<Selection>(
		new Set(),
	);

	// 动态计算父节点键和展开状态
	const parentNodeKeys = useMemo(() => {
		if (!navigationData) {
			return new Set(["rules-intro", "section-switch"]);
		}

		// 从动态数据中提取父节点键
		const parentKeys = navigationData
			.filter(node => node.children && node.children.length > 0)
			.map(node => node.id);

		return new Set(parentKeys);
	}, [navigationData]);

	// 管理 TreeView 展开状态
	const [expandedTreeKeys, setExpandedTreeKeys] = useState<Set<Key>>(
		new Set(),
	);

	// 当导航数据变化时，更新展开状态和设置默认选中首页
	// 只有在配置数据也就绪时才触发默认选择，避免双重渲染
	useEffect(() => {
		if (navigationData) {
			// 默认展开所有父节点
			const parentKeys = navigationData
				.filter(node => node.children && node.children.length > 0)
				.map(node => node.id);
			setExpandedTreeKeys(new Set(parentKeys));

			// 检查是否存在首页节点，如果存在且配置数据就绪则设置为默认选中
			const hasHomeNode = navigationData.some(node =>
				node.id === 'home' ||
				(node.children && node.children.some(child => child.id === 'home'))
			);
			if (hasHomeNode && configurationDataReady) {
				setSelectedTreeKeys(new Set(['home']));
				// 触发选择变化回调，激活对应的骨架屏和按钮组
				if (onSelectionChangeRef.current) {
					onSelectionChangeRef.current('home');
				}
			} else if (!configurationDataReady) {
				// 如果配置数据未就绪，暂不设置默认选择
				setSelectedTreeKeys(new Set());
				// 暂不通知父组件，等待配置数据就绪
			} else {
				// 如果没有首页节点，重置选择状态
				setSelectedTreeKeys(new Set());
				// 通知父组件清空选择
				if (onSelectionChangeRef.current) {
					onSelectionChangeRef.current(null);
				}
			}
		} else {
			// 清空展开状态和选择状态
			setExpandedTreeKeys(new Set());
			setSelectedTreeKeys(new Set());
			// 通知父组件清空选择
			if (onSelectionChangeRef.current) {
				onSelectionChangeRef.current(null);
			}
		}
	}, [navigationData, configurationDataReady]);

	// 获取所有子节点键（用于全选处理）
	const getAllChildKeys = useMemo(() => {
		if (!navigationData) {
			// 静态数据的子节点键
			return new Set([
				"home",
				"rules-qa",
				"rules-onestation",
				"rules-timerace",
				"rules-finalpk",
				"rules-tiebreak",
				"rules-scoring",
				"switch-qa",
				"switch-onestation",
				"switch-timerace",
				"switch-finalpk",
				"switch-tiebreak",
				"switch-ranking",
			]);
		}

		// 从动态数据中提取所有子节点键
		const childKeys = new Set<string>();
		navigationData.forEach(node => {
			if (!node.children || node.children.length === 0) {
				// 叶子节点
				childKeys.add(node.id);
			} else {
				// 父节点的子节点
				node.children.forEach(child => {
					childKeys.add(child.id);
				});
			}
		});
		return childKeys;
	}, [navigationData]);

	// 处理 TreeView 选择变化 - 过滤父节点选择
	const handleTreeSelectionChange = (keys: Selection) => {
		if (keys === "all") {
			// 如果是全选，过滤掉父节点
			setSelectedTreeKeys(getAllChildKeys);
			// 通知父组件选择变化（取第一个选中的键）
			if (onSelectionChange) {
				const firstKey = Array.from(getAllChildKeys)[0];
				onSelectionChange(firstKey || null);
			}
		} else {
			// 过滤掉父节点键
			const filteredKeys = new Set(
				Array.from(keys).filter((key) => !parentNodeKeys.has(key as string)),
			);
			setSelectedTreeKeys(filteredKeys);
			// 通知父组件选择变化
			if (onSelectionChange) {
				const selectedKey =
					filteredKeys.size > 0
						? (Array.from(filteredKeys)[0] as string)
						: null;
				onSelectionChange(selectedKey);
			}
		}
	};

	// 处理 TreeView 展开状态变化
	const handleTreeExpandedChange = (keys: Set<Key>) => {
		setExpandedTreeKeys(keys);
	};

	// 处理 TreeView 项目动作 - 父节点点击时切换展开/折叠状态
	const handleTreeAction = (key: Key) => {
		const keyString = key as string;
		if (parentNodeKeys.has(keyString)) {
			// 如果是父节点，切换展开/折叠状态
			const newExpandedKeys = new Set(expandedTreeKeys);
			if (newExpandedKeys.has(key)) {
				newExpandedKeys.delete(key);
			} else {
				newExpandedKeys.add(key);
			}
			setExpandedTreeKeys(newExpandedKeys);
		}
		// 子节点的 action 不做处理，保持默认行为
	};

	// 渲染单个导航节点
	const renderNavigationNode = (node: NavigationNode, index: number) => {
		const IconComponent = getIconComponent(node.icon);
		// 使用组合键确保唯一性，避免React Spectrum的ID冲突
		const uniqueKey = `nav-${node.id}-${index}`;

		if (node.children && node.children.length > 0) {
			// 父节点
			return (
				<TreeViewItem key={uniqueKey} id={node.id} textValue={node.name}>
					<TreeViewItemContent>
						<Text>{node.name}</Text>
						<IconComponent />
					</TreeViewItemContent>
					{node.children.map((child, childIndex) => renderNavigationNode(child, childIndex))}
				</TreeViewItem>
			);
		} else {
			// 叶子节点
			return (
				<TreeViewItem key={uniqueKey} id={node.id} textValue={node.name}>
					<TreeViewItemContent>
						<Text>{node.name}</Text>
						<IconComponent />
					</TreeViewItemContent>
				</TreeViewItem>
			);
		}
	};



	// 生成TreeView的key，确保数据变化时重新创建组件
	const treeViewKey = useMemo(() => {
		if (loading) return 'loading';
		if (navigationData && navigationData.length > 0) {
			return `dynamic-${navigationData.length}-${navigationData.map(n => n.id).join('-')}`;
		}
		return 'no-data';
	}, [loading, navigationData]);

	// 调试：渲染逻辑
	console.log('[NavigationTreeView] 🎨 渲染逻辑调试', {
		treeViewKey,
		loading,
		navigationDataExists: !!navigationData,
		navigationDataLength: navigationData?.length || 0,
		willRenderNodes: !loading && navigationData && navigationData.length > 0,
		timestamp: Date.now(),
		action: 'render_logic_debug'
	});

	return (
		<TreeView
			key={treeViewKey}
			aria-label={ariaLabel}
			expandedKeys={expandedTreeKeys}
			onExpandedChange={handleTreeExpandedChange}
			height={height}
			maxWidth={maxWidth}
			selectionMode="single"
			selectionStyle="highlight"
			disallowEmptySelection
			defaultSelectedKeys={['home']}
			selectedKeys={selectedTreeKeys}
			onSelectionChange={handleTreeSelectionChange}
			onAction={handleTreeAction}
			UNSAFE_className={`navigation-tree-view ${className || ""}`}
			UNSAFE_style={{
				overflow: "auto",
				flex: 1,
			}}
		>
			{loading ? (
				// 加载状态显示
				<TreeViewItem id="loading" textValue="加载中...">
					<TreeViewItemContent>
						<Text>正在加载导航数据...</Text>
						<FileTxt />
					</TreeViewItemContent>
				</TreeViewItem>
			) : navigationData && navigationData.length > 0 ? (
				// 使用动态数据渲染
				navigationData.map((node, index) => renderNavigationNode(node, index))
			) : (
				// 调试：无数据状态
				<TreeViewItem id="no-data" textValue="无导航数据">
					<TreeViewItemContent>
						<Text>无导航数据 - 调试模式</Text>
						<FileTxt />
					</TreeViewItemContent>
				</TreeViewItem>
			)}
		</TreeView>
	);
}

export default NavigationTreeView;
