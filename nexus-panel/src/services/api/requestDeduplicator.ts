/**
 * 全局请求去重器
 * 
 * 用于防止在整个应用中发起重复的API请求
 */

// 全局请求缓存
const globalRequestCache = new Map<string, Promise<unknown>>();

// 请求计数器，用于调试
const requestCounters = new Map<string, number>();

/**
 * 全局请求去重器
 */
export class GlobalRequestDeduplicator {
    /**
     * 执行去重的请求
     * @param key 请求的唯一标识符
     * @param requestFunction 请求函数
     * @returns 请求结果
     */
    static async execute<T>(
        key: string,
        requestFunction: () => Promise<T>
    ): Promise<T> {
        // 更新请求计数
        const currentCount = (requestCounters.get(key) || 0) + 1;
        requestCounters.set(key, currentCount);

        console.log(`[GlobalRequestDeduplicator] 🔍 请求去重检查`, {
            key,
            requestCount: currentCount,
            hasPendingRequest: globalRequestCache.has(key),
            totalPendingRequests: globalRequestCache.size,
            timestamp: Date.now(),
            action: 'request_deduplication_check'
        });

        // 检查是否有正在进行的相同请求
        if (globalRequestCache.has(key)) {
            console.log(`[GlobalRequestDeduplicator] ⏳ 检测到重复请求，等待现有请求完成`, {
                key,
                requestCount: currentCount,
                timestamp: Date.now(),
                action: 'request_deduplication_hit'
            });
            return globalRequestCache.get(key)! as Promise<T>;
        }

        // 创建新的请求Promise
        const requestPromise = (async (): Promise<T> => {
            try {
                console.log(`[GlobalRequestDeduplicator] 🚀 执行新请求`, {
                    key,
                    requestCount: currentCount,
                    timestamp: Date.now(),
                    action: 'request_deduplication_execute'
                });

                const result = await requestFunction();

                console.log(`[GlobalRequestDeduplicator] ✅ 请求完成`, {
                    key,
                    requestCount: currentCount,
                    timestamp: Date.now(),
                    action: 'request_deduplication_success'
                });

                return result;
            } catch (error) {
                console.error(`[GlobalRequestDeduplicator] ❌ 请求失败`, {
                    key,
                    requestCount: currentCount,
                    error: error instanceof Error ? error.message : String(error),
                    timestamp: Date.now(),
                    action: 'request_deduplication_error'
                });
                throw error;
            } finally {
                // 请求完成后清理缓存
                globalRequestCache.delete(key);
                console.log(`[GlobalRequestDeduplicator] 🧹 清理请求缓存`, {
                    key,
                    requestCount: currentCount,
                    remainingRequests: globalRequestCache.size,
                    timestamp: Date.now(),
                    action: 'request_deduplication_cleanup'
                });
            }
        })();

        // 缓存正在进行的请求
        globalRequestCache.set(key, requestPromise);

        return requestPromise;
    }

    /**
     * 获取请求统计信息
     */
    static getStats() {
        return {
            pendingRequests: globalRequestCache.size,
            requestCounters: Object.fromEntries(requestCounters.entries()),
            pendingKeys: Array.from(globalRequestCache.keys()),
            timestamp: Date.now()
        };
    }

    /**
     * 清理所有缓存
     */
    static clearAll() {
        globalRequestCache.clear();
        requestCounters.clear();
        console.log(`[GlobalRequestDeduplicator] 🧹 清理所有请求缓存`, {
            timestamp: Date.now(),
            action: 'request_deduplication_clear_all'
        });
    }
}