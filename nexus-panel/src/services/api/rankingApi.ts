/**
 * 排名相关 API 服务
 * 
 * 提供排名数据的获取、处理和计算功能
 */

import { httpClient } from './client';
import type {
	AnswerRecordApiResponse,
	PlayerInfoApiResponse,
	TableStructureResponse,
	RankingData,
	PlayerScore,
	RankingProgress,
	AnswerRecordApiItem,
	PlayerInfoApiItem,
	ApiResponse
} from './types';
import { ApiErrorHandler, createDataFormatError } from './errors';
import { FieldMapper, MappingDirection } from './adapters/FieldMapper';
import { TypeConverter } from './adapters/TypeConverter';

// ==================== API 配置 ====================

/**
 * 排名 API 配置
 */
const RANKING_API_CONFIG = {
	/** 认证 Token */
	token: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
	/** 默认分页大小 - 避免数据被分页截断 */
	defaultPageSize: 500,
} as const;

// ==================== 数据验证函数 ====================

/**
 * 验证答题记录 API 响应数据格式
 */
function validateAnswerRecordApiResponse(response: unknown): response is AnswerRecordApiResponse {
	if (!response || typeof response !== 'object') {
		return false;
	}

	const data = response as Record<string, unknown>;
	return (
		Array.isArray(data.list) &&
		typeof data.pageInfo === 'object' &&
		data.pageInfo !== null
	);
}

/**
 * 验证选手信息 API 响应数据格式
 */
function validatePlayerInfoApiResponse(response: unknown): response is PlayerInfoApiResponse {
	if (!response || typeof response !== 'object') {
		return false;
	}

	const data = response as Record<string, unknown>;
	return (
		Array.isArray(data.list) &&
		typeof data.pageInfo === 'object' &&
		data.pageInfo !== null
	);
}

// ==================== 数据处理函数 ====================

/**
 * 计算选手排名的核心算法（严格连续序号排列）
 * 排序规则：
 * 1. 第一优先级：总分降序
 * 2. 第二优先级：选手ID升序（确保结果一致性）
 * 排名序号：连续整数序列 1、2、3、4、5...
 * @param answerRecords 答题记录数据
 * @param playerInfo 选手信息数据
 * @returns 计算后的选手得分排名数据
 */
function calculateRankings(
	answerRecords: AnswerRecordApiItem[],
	playerInfo: PlayerInfoApiItem[]
): PlayerScore[] {
	// 创建选手基础信息映射
	const playerMap = new Map<string, PlayerScore>();

	// 初始化所有选手的基础数据
	playerInfo.forEach(player => {
		playerMap.set(String(player.user_id), {
			playerId: String(player.user_id),
			playerName: player.user_name,
			stageScores: {},
			totalScore: 0
		});
	});

	// 统计所有环节
	const stagesSet = new Set<string>();

	// 处理答题记录，累计得分
	answerRecords.forEach(record => {
		const playerId = String(record.user_id);
		const stageName = record.session_id;
		const isCorrect = record.is_correct === 1;
		const score = isCorrect ? record.score : 0;

		stagesSet.add(stageName);

		const player = playerMap.get(playerId);
		if (player) {
			// 累加环节得分
			if (!player.stageScores[stageName]) {
				player.stageScores[stageName] = 0;
			}
			player.stageScores[stageName] += score;
		}
	});

	// 计算总分并转换为数组
	const players = Array.from(playerMap.values()).map(player => {
		player.totalScore = Object.values(player.stageScores).reduce((sum, score) => sum + score, 0);
		return player;
	});

	// 按总分降序排序，总分相同时按选手ID升序排序（确保连续排名）
	players.sort((a, b) => {
		// 第一优先级：总分降序
		if (b.totalScore !== a.totalScore) {
			return b.totalScore - a.totalScore;
		}
		// 第二优先级：选手ID升序（确保结果一致性和可预测性）
		return a.playerId.localeCompare(b.playerId);
	});

	// 分配连续排名序号（1、2、3、4、5...）
	players.forEach((player, index) => {
		player.rank = index + 1;
	});

	return players;
}

// ==================== API 服务类 ====================

/**
 * 排名 API 服务类
 */
export class RankingApiService {
	/**
	 * 获取答题记录数据（支持分页）
	 */
	static async getAnswerRecords(
		tableId: string,
		page: number = 1,
		pageSize: number = RANKING_API_CONFIG.defaultPageSize
	): Promise<ApiResponse<AnswerRecordApiResponse>> {
		try {
			// 构建请求端点
			const endpoint = `https://noco.ohvfx.com/api/v2/tables/${tableId}/records`;

			// 构建请求参数
			const params = {
				limit: pageSize,
				page: page,
			};

			// 构建请求头
			const headers = {
				'xc-token': RANKING_API_CONFIG.token,
			};

			// 发起请求
			const response = await httpClient.get<AnswerRecordApiResponse>(
				endpoint,
				params,
				{ headers }
			);

			// 验证响应数据格式
			if (!validateAnswerRecordApiResponse(response.data)) {
				throw createDataFormatError('答题记录 API 响应数据格式不正确');
			}

			return response;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RankingApiService.getAnswerRecords');
			throw apiError;
		}
	}

	/**
	 * 获取选手信息数据
	 */
	static async getPlayerInfo(tableId: string): Promise<ApiResponse<PlayerInfoApiResponse>> {
		try {
			// 构建请求端点
			const endpoint = `https://noco.ohvfx.com/api/v2/tables/${tableId}/records`;

			// 构建请求参数
			const params = {
				limit: RANKING_API_CONFIG.defaultPageSize, // 使用统一的分页配置
			};

			// 构建请求头
			const headers = {
				'xc-token': RANKING_API_CONFIG.token,
			};

			// 发起请求
			const rawResponse = await httpClient.get<any>(
				endpoint,
				params,
				{ headers }
			);

			// 验证原始响应数据格式
			if (!rawResponse.data || !Array.isArray(rawResponse.data.list)) {
				throw createDataFormatError('选手信息 API 响应数据格式不正确');
			}

			console.log('[选手信息API] 原始响应数据:', {
				samplePlayer: rawResponse.data.list[0] ? Object.keys(rawResponse.data.list[0]) : 'no players',
				totalPlayers: rawResponse.data.list.length
			});

			// 使用字段映射适配器转换选手信息数据
			const playerMappingResult = FieldMapper.mapArrayFields(
				rawResponse.data.list,
				'player',
				MappingDirection.FROM_API,
				{
					keepUnmappedFields: true,
					applyDefaults: true,
					convertTypes: true,
					debug: true
				}
			);

			console.log('[选手信息API] 字段映射结果:', {
				stats: playerMappingResult.stats,
				warnings: playerMappingResult.warnings
			});

			// 应用类型转换
			const playerTypeResult = TypeConverter.convertBatch(
				playerMappingResult.data,
				'player',
				{ applyDefaults: true, debug: true }
			);

			// 构建最终响应
			const response: ApiResponse<PlayerInfoApiResponse> = {
				data: {
					list: playerTypeResult.data as PlayerInfoApiItem[],
					pageInfo: rawResponse.data.pageInfo
				},
				status: rawResponse.status,
				headers: rawResponse.headers
			};

			// 验证转换后的响应数据格式
			if (!validatePlayerInfoApiResponse(response.data)) {
				throw createDataFormatError('选手信息 API 响应数据格式不正确');
			}

			return response;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'RankingApiService.getPlayerInfo');
			throw apiError;
		}
	}

	/**
	 * 获取表结构信息
	 */
	static async getTableStructure(baseId: string): Promise<ApiResponse<TableStructureResponse>> {
		// 使用全局请求去重器防止重复请求
		const { GlobalRequestDeduplicator } = await import('./requestDeduplicator');

		return GlobalRequestDeduplicator.execute(
			`table_structure_${baseId}`,
			async () => {
				try {
					// 构建请求端点
					const endpoint = `https://noco.ohvfx.com/api/v2/meta/bases/${baseId}/tables`;

					// 构建请求头
					const headers = {
						'xc-token': RANKING_API_CONFIG.token,
					};

					console.log(`[RankingApiService] 🚀 发起表结构API请求`, {
						baseId,
						endpoint,
						timestamp: Date.now(),
						action: 'ranking_api_table_structure_request'
					});

					// 发起请求
					const response = await httpClient.get<TableStructureResponse>(
						endpoint,
						undefined, // 无查询参数
						{ headers }
					);

					console.log(`[RankingApiService] ✅ 表结构API请求成功`, {
						baseId,
						tablesCount: response.data.list.length,
						timestamp: Date.now(),
						action: 'ranking_api_table_structure_success'
					});

					return response;

				} catch (error) {
					// 记录错误并重新抛出
					const apiError = ApiErrorHandler.createApiError(error);
					ApiErrorHandler.logError(apiError, 'RankingApiService.getTableStructure');
					throw apiError;
				}
			}
		);
	}

	/**
	 * 完整的排名数据获取（带进度回调）
	 * @param baseId 项目基础ID
	 * @param onProgress 进度回调函数
	 * @param getTableStructureWithCache 可选的缓存表结构获取函数
	 */
	static async getRankingData(
		baseId: string,
		onProgress?: (progress: RankingProgress) => void,
		getTableStructureWithCache?: (baseId: string) => Promise<TableStructureResponse>
	): Promise<RankingData> {
		try {
			// 阶段1：获取表结构信息
			onProgress?.({
				stage: 'table_structure',
				progress: 10,
				message: '正在获取表结构信息...'
			});

			// 优先使用缓存函数，否则使用直接API调用
			let tables: TableStructureResponse['list'];
			if (getTableStructureWithCache) {
				const tableStructureData = await getTableStructureWithCache(baseId);
				tables = tableStructureData.list;
			} else {
				const tableStructureResponse = await this.getTableStructure(baseId);
				tables = tableStructureResponse.data.list;
			}

			// 查找答题记录表和选手表
			const answerRecordTable = tables.find(table => table.title === "答题记录表");
			const playerTable = tables.find(table => table.title === "选手表");

			if (!answerRecordTable) {
				throw new Error('未找到答题记录表');
			}
			if (!playerTable) {
				throw new Error('未找到选手表');
			}

			// 阶段2：获取选手信息（通常数据量较小，先获取）
			onProgress?.({
				stage: 'player_info',
				progress: 20,
				message: '正在获取选手信息...'
			});

			const playerInfoResponse = await this.getPlayerInfo(playerTable.id);
			const playerInfo = playerInfoResponse.data.list;

			// 阶段3：分页获取答题记录数据
			onProgress?.({
				stage: 'answer_records',
				progress: 30,
				message: '正在获取答题记录...'
			});

			const allAnswerRecords: AnswerRecordApiItem[] = [];
			let currentPage = 1;
			let totalPages = 1;

			// 获取第一页以确定总页数
			const firstPageResponse = await this.getAnswerRecords(answerRecordTable.id, 1, RANKING_API_CONFIG.defaultPageSize);
			const firstPageData = firstPageResponse.data;
			allAnswerRecords.push(...firstPageData.list);

			totalPages = Math.ceil(firstPageData.pageInfo.totalRows / firstPageData.pageInfo.pageSize);

			// 获取剩余页面
			for (currentPage = 2; currentPage <= totalPages; currentPage++) {
				const progressPercent = 30 + Math.floor((currentPage - 1) / totalPages * 50);
				onProgress?.({
					stage: 'answer_records',
					progress: progressPercent,
					message: `正在获取答题记录... (第${currentPage}页/共${totalPages}页)`,
					currentPage,
					totalPages
				});

				const pageResponse = await this.getAnswerRecords(answerRecordTable.id, currentPage, RANKING_API_CONFIG.defaultPageSize);
				allAnswerRecords.push(...pageResponse.data.list);
			}

			// 阶段4：计算排名
			onProgress?.({
				stage: 'calculating',
				progress: 90,
				message: '正在计算排名...'
			});

			const players = calculateRankings(allAnswerRecords, playerInfo);
			const stages = Array.from(new Set(allAnswerRecords.map(record => record.session_id)));

			// 完成
			onProgress?.({
				stage: 'complete',
				progress: 100,
				message: '排名数据获取完成'
			});

			return {
				players,
				stages,
				lastUpdated: Date.now(),
				totalPlayers: playerInfo.length
			};

		} catch (error) {
			// 详细的错误处理和恢复策略
			const apiError = ApiErrorHandler.createApiError(error);

			// 根据错误类型提供不同的处理策略
			if (apiError.message.includes('网络')) {
				// 网络错误：建议重试
				apiError.message += ' - 请检查网络连接后重试';
			} else if (apiError.message.includes('未找到')) {
				// 数据缺失错误：提供具体指导
				apiError.message += ' - 请确认项目配置是否正确';
			} else if (apiError.message.includes('格式')) {
				// 数据格式错误：提供调试信息
				apiError.message += ' - 数据格式可能已变更，请联系技术支持';
			}

			// 记录详细错误信息
			ApiErrorHandler.logError(apiError, 'RankingApiService.getRankingData');

			// 通知进度回调错误状态
			onProgress?.({
				stage: 'complete',
				progress: 0,
				message: `获取失败: ${apiError.message}`
			});

			throw apiError;
		}
	}
}

// ==================== 便捷函数导出 ====================

/**
 * 获取排名数据的便捷函数
 */
export const getRankingData = (
	baseId: string,
	onProgress?: (progress: RankingProgress) => void,
	getTableStructureWithCache?: (baseId: string) => Promise<TableStructureResponse>
) => RankingApiService.getRankingData(baseId, onProgress, getTableStructureWithCache);

/**
 * 获取答题记录的便捷函数
 */
export const getAnswerRecords = RankingApiService.getAnswerRecords.bind(RankingApiService);

/**
 * 获取选手信息的便捷函数
 */
export const getPlayerInfo = RankingApiService.getPlayerInfo.bind(RankingApiService);

// 简单的内存缓存机制
const sectionRankingCache = new Map<string, { data: RankingData; timestamp: number }>();
const CACHE_DURATION = 30000; // 30秒缓存

// 定期清理过期缓存
setInterval(() => {
	const now = Date.now();
	for (const [key, value] of sectionRankingCache.entries()) {
		if (now - value.timestamp > CACHE_DURATION) {
			sectionRankingCache.delete(key);
		}
	}
}, CACHE_DURATION); // 每30秒清理一次

/**
 * 获取指定环节的排名数据（带缓存和错误处理优化）
 * @param baseId 项目基础ID
 * @param sectionName 环节名称（如"争分夺秒"、"同分加赛"）
 * @param getTableStructureWithCache 可选的缓存表结构获取函数
 * @returns 环节排名数据
 */
// 防止并发请求的Promise缓存
const pendingSectionRankingRequests = new Map<string, Promise<RankingData>>();

export async function getSectionRankingData(
	baseId: string,
	sectionName: string,
	getTableStructureWithCache?: (baseId: string) => Promise<TableStructureResponse>
): Promise<RankingData> {
	// 强制日志 - 确认函数被调用
	console.log(`[getSectionRankingData] 函数开始执行`, {
		baseId,
		sectionName,
		timestamp: Date.now(),
		action: 'function_start'
	});

	// 创建请求唯一标识符
	const requestKey = `${baseId}_${sectionName}`;

	// 检查是否有正在进行的相同请求
	if (pendingSectionRankingRequests.has(requestKey)) {
		console.log(`[getSectionRankingData] 检测到正在进行的${sectionName}排名请求，等待结果`, {
			baseId,
			sectionName,
			requestKey,
			timestamp: Date.now(),
			action: 'section_ranking_request_deduplication'
		});
		return pendingSectionRankingRequests.get(requestKey)!;
	}

	// 检查缓存
	const cacheKey = `${baseId}-${sectionName}`;
	const cached = sectionRankingCache.get(cacheKey);
	const now = Date.now();

	if (cached && (now - cached.timestamp) < CACHE_DURATION) {
		// 返回缓存数据
		return cached.data;
	}

	// 创建请求Promise并缓存
	const requestPromise = (async (): Promise<RankingData> => {
		// 声明变量用于跟踪表结构数据来源
		let tableStructureSource: 'cache_function' | 'direct_api' = 'direct_api';

		try {
			// 获取表结构信息（优先使用缓存，增强日志记录）
			let tables: TableStructureResponse['list'];

			if (getTableStructureWithCache) {
				// 使用缓存函数获取表结构（推荐方式，避免重复API调用）
				console.log(`[getSectionRankingData] 使用缓存函数获取${sectionName}表结构`, {
					baseId,
					sectionName,
					source: 'cache_function',
					timestamp: Date.now(),
					action: 'get_section_ranking_use_cache_function'
				});

				const tableStructureData = await getTableStructureWithCache(baseId);
				tables = tableStructureData.list;
				tableStructureSource = 'cache_function';

				console.log(`[getSectionRankingData] 缓存函数返回表结构数据`, {
					baseId,
					sectionName,
					tablesCount: tables.length,
					availableTables: tables.map(t => t.title),
					timestamp: Date.now(),
					action: 'get_section_ranking_cache_function_result'
				});

				// 详细输出表结构信息用于调试
				console.log(`[getSectionRankingData] 详细表结构信息:`, {
					baseId,
					sectionName,
					tables: tables.map(t => ({ id: t.id, title: t.title })),
					timestamp: Date.now()
				});

				// 强制输出每个表的名称，用于调试表名匹配问题
				console.log(`[getSectionRankingData] 所有表名列表:`, {
					baseId,
					sectionName,
					tableNames: tables.map(t => t.title),
					searchingFor: ['答题记录表', '选手信息表'],
					timestamp: Date.now()
				});

				// 强制输出每个表的详细信息
				tables.forEach((table, index) => {
					console.log(`[getSectionRankingData] 表 ${index + 1}:`, {
						id: table.id,
						title: table.title,
						type: table.type,
						baseId,
						sectionName,
						timestamp: Date.now()
					});
				});
			} else {
				// 直接调用API获取表结构（可能导致重复请求）
				console.warn(`[getSectionRankingData] 未提供缓存函数，直接调用API获取${sectionName}表结构`, {
					baseId,
					sectionName,
					source: 'direct_api',
					warning: 'potential_duplicate_request',
					timestamp: Date.now(),
					action: 'get_section_ranking_use_direct_api'
				});

				const tableStructureResponse = await RankingApiService.getTableStructure(baseId);
				tables = tableStructureResponse.data.list;
				tableStructureSource = 'direct_api';
			}

			// 查找答题记录表和选手表（尝试多个可能的表名）
			const answerRecordTable = tables.find(table => table.title === "答题记录表");

			// 尝试多个可能的选手表名称
			const possiblePlayerTableNames = ["选手信息表", "选手表", "参赛选手表", "选手名单表", "选手列表"];
			let playerTable = null;

			for (const tableName of possiblePlayerTableNames) {
				playerTable = tables.find(table => table.title === tableName);
				if (playerTable) {
					console.log(`[getSectionRankingData] 找到选手表: ${tableName}`, {
						baseId,
						sectionName,
						foundTableName: tableName,
						tableId: playerTable.id,
						timestamp: Date.now()
					});
					break;
				}
			}

			if (!answerRecordTable) {
				throw new Error('未找到答题记录表');
			}
			if (!playerTable) {
				// 提供更详细的错误信息，包含实际的表名列表
				const actualTableNames = tables.map(t => t.title).join(', ');
				throw new Error(`未找到选手表。尝试的表名: ${possiblePlayerTableNames.join(', ')}。实际表名: ${actualTableNames}`);
			}

			// 获取选手信息
			const playerInfoResponse = await RankingApiService.getPlayerInfo(playerTable.id);
			const playerInfo = playerInfoResponse.data.list;

			// 获取答题记录，使用环节过滤（修复：使用httpClient统一API请求方式）
			console.log(`[getSectionRankingData] 开始获取${sectionName}答题记录`, {
				baseId,
				sectionName,
				answerRecordTableId: answerRecordTable.id,
				timestamp: Date.now(),
				action: 'get_answer_records_start'
			});

			const answerRecordResponse = await httpClient.get<AnswerRecordApiResponse>(
				`https://noco.ohvfx.com/api/v2/tables/${answerRecordTable.id}/records`,
				{
					where: `(所属环节,eq,${sectionName})~and(question_pack_id,eq,1)~and(stage,eq,通用题)`,
					limit: 1000
				},
				{
					headers: {
						'xc-token': 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
					}
				}
			);

			console.log(`[getSectionRankingData] ${sectionName}答题记录API请求成功`, {
				baseId,
				sectionName,
				responseStatus: answerRecordResponse.status,
				hasData: !!answerRecordResponse.data,
				timestamp: Date.now(),
				action: 'get_answer_records_success'
			});

			if (!answerRecordResponse.data) {
				throw new Error(`获取${sectionName}答题记录失败: 无响应数据`);
			}

			const rawAnswerRecords = answerRecordResponse.data.list || [];

			console.log('[排名API] 原始答题记录数据:', {
				sampleRecord: rawAnswerRecords[0] ? Object.keys(rawAnswerRecords[0]) : 'no records',
				totalRecords: rawAnswerRecords.length
			});

			// 使用字段映射适配器转换答题记录数据
			const answerRecordMappingResult = FieldMapper.mapArrayFields(
				rawAnswerRecords,
				'answer_record',
				MappingDirection.FROM_API,
				{
					keepUnmappedFields: true,
					applyDefaults: true,
					convertTypes: true,
					debug: true
				}
			);

			console.log('[排名API] 答题记录字段映射结果:', {
				stats: answerRecordMappingResult.stats,
				warnings: answerRecordMappingResult.warnings
			});

			// 应用类型转换
			const answerRecordTypeResult = TypeConverter.convertBatch(
				answerRecordMappingResult.data,
				'answer_record',
				{ applyDefaults: true, debug: true }
			);

			const answerRecords = answerRecordTypeResult.data as AnswerRecordApiItem[];

			// 处理数据并计算排名
			const { calculatePlayerRanks, convertApiDataToPlayerScores } = await import('../../utils/rankingUtils');

			let players, rankedPlayers, stages;

			if (answerRecords.length === 0) {
				// 没有答题记录时，创建基础的选手列表（所有分数为0）
				console.log(`[getSectionRankingData] 没有${sectionName}答题记录，创建基础选手列表`, {
					baseId,
					sectionName,
					playerCount: playerInfo.length,
					timestamp: Date.now(),
					action: 'create_basic_player_list'
				});

				// 创建基础选手数据（所有分数为0）
				players = playerInfo.map((player, index) => ({
					playerId: String(player.user_id), // 使用新的字段名
					playerName: String(player.user_name || `选手${player.user_id}`), // 使用新的字段名
					stageScores: { [sectionName]: 0 }, // 该环节分数为0
					totalScore: 0,
					rank: index + 1 // 暂时按ID顺序排名
				}));

				rankedPlayers = players; // 基础列表不需要重新排名
				stages = [sectionName]; // 只有当前环节
			} else {
				// 有答题记录时，正常处理数据
				console.log(`[getSectionRankingData] 处理${sectionName}答题记录`, {
					baseId,
					sectionName,
					recordCount: answerRecords.length,
					playerCount: playerInfo.length,
					timestamp: Date.now(),
					action: 'process_answer_records'
				});

				players = convertApiDataToPlayerScores(answerRecords, playerInfo);
				rankedPlayers = calculatePlayerRanks(players);

				// 提取环节列表
				const stagesSet = new Set<string>();
				answerRecords.forEach((record: AnswerRecordApiItem) => {
					stagesSet.add(record.session_id);
				});
				stages = Array.from(stagesSet);
			}

			const result: RankingData = {
				players: rankedPlayers,
				stages,
				lastUpdated: Date.now(),
				totalPlayers: playerInfo.length
			};

			// 存储到缓存
			sectionRankingCache.set(cacheKey, {
				data: result,
				timestamp: Date.now()
			});

			// 记录最终的数据获取结果和表结构来源
			console.log(`[getSectionRankingData] ${sectionName}排名数据获取成功`, {
				baseId,
				sectionName,
				tableStructureSource,
				totalPlayers: result.totalPlayers,
				stagesCount: result.stages.length,
				dataSource: tableStructureSource === 'cache_function' ? '优化路径（使用缓存）' : '直接API路径',
				cacheStored: true,
				timestamp: Date.now(),
				action: 'get_section_ranking_success'
			});

			return result;

		} catch (error) {
			const apiError = ApiErrorHandler.createApiError(error);
			apiError.message = `获取${sectionName}排名数据失败: ${apiError.message}`;

			// 记录错误信息，包含表结构来源
			console.error(`[getSectionRankingData] ${sectionName}排名数据获取失败`, {
				baseId,
				sectionName,
				tableStructureSource: tableStructureSource || 'unknown',
				error: apiError.message,
				timestamp: Date.now(),
				action: 'get_section_ranking_error'
			});

			ApiErrorHandler.logError(apiError, 'getSectionRankingData');
			throw apiError;
		}
	})();

	// 缓存正在进行的请求
	pendingSectionRankingRequests.set(requestKey, requestPromise);

	try {
		const result = await requestPromise;
		return result;
	} finally {
		// 请求完成后清理pending状态
		pendingSectionRankingRequests.delete(requestKey);
	}
}
