/**
 * MQTT 集成 Hook
 * 
 * 封装所有与 MQTT 相关的业务逻辑，包括：
 * - 连接管理和状态监控
 * - 消息发布和订阅处理
 * - 内存管理集成
 * - 错误处理和日志记录
 * - 业务消息映射和发送
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useMQTT } from './useMQTT';
import { useMQTTMemoryManager } from './useMQTTMemoryManager';
import {
	MQTTUtils,
	MQTTPresets,
	MQTTDomain,
	MQTTContext,
	MQTTAction,
	MQTTTarget,
	type MQTTMessage,
	type MQTTMemoryStats,
	MQTTConnectionStatus,
} from '../services/mqtt';

/**
 * MQTT 集成配置选项
 */
export interface MQTTIntegrationOptions {
	/** 日志记录回调函数 */
	onLog: (level: 'info' | 'warning' | 'error' | 'success' | 'send' | 'get', message: string, details?: unknown) => void;
	/** 内存统计更新回调函数 */
	onMemoryStatsUpdate: () => void;
	/** 获取当前日志数量的回调函数 */
	getLogCount: () => number;
	/** 清理日志的回调函数 */
	onLogCleanup: (count: number) => void;
	/** 赛事 API Hook 返回值，用于查找赛事信息 */
	raceApi: ReturnType<typeof import('./useRaceApi').useRaceApi>;
	/** 是否启用调试模式，默认根据环境变量决定 */
	debug?: boolean;
}

/**
 * MQTT 集成返回值接口
 */
export interface MQTTIntegrationReturn {
	/** 连接状态 */
	connectionStatus: MQTTConnectionStatus;
	/** 是否已连接 */
	isConnected: boolean;
	/** 连接错误信息 */
	error: string | null;
	/** 内存统计信息 */
	memoryStats: MQTTMemoryStats | null;

	/** 处理项目选择变化 */
	handleProjectChange: (projectKey: string | null) => void;
	/** 处理导航选择变化 */
	handleNavigationChange: (navigationKey: string | null) => void;
	/** 手动重连 */
	handleReconnect: () => Promise<void>;
}

/**
 * 导航到 MQTT 消息的映射配置
 */
const NAVIGATION_MQTT_MAPPING = {
	// 规则显示映射
	rules: {
		qa: { id: 1, name: "有问必答规则" },
		onestation: { id: 2, name: "一站到底规则" },
		timerace: { id: 3, name: "争分夺秒规则" },
		finalpk: { id: 4, name: "终极PK规则" },
		tiebreak: { id: 5, name: "同分加赛规则" },
		scoring: { id: 6, name: "积分办法" },
	},
	// 环节切换映射
	sessions: {
		qa: { id: 1, name: "有问必答" },
		onestation: { id: 3, name: "一站到底" },
		timerace: { id: 4, name: "争分夺秒" },
		finalpk: { id: 7, name: "终极PK" },
		tiebreak: { id: 5, name: "同分加赛" },
		ranking: { id: 8, name: "总分排名" },
	}
};

/**
 * 项目到赛事的映射配置
 */
const PROJECT_RACE_MAPPING = ["", "功能测试", "第一阶段", "第二阶段", "决赛阶段"];

/**
 * 根据ContentToggle的状态获取MQTT消息的目标设备
 *
 * @param contentType - ContentToggle组件的当前状态 ("global" | "targeted")
 * @param specificTarget - 当选择定向指令时的具体目标设备 (可选)
 * @returns MQTT消息的target字段值
 *
 * 使用示例：
 * - 全局指令：getMQTTTarget("global") -> "all"
 * - 定向指令：getMQTTTarget("targeted", "player-1") -> "player-1"
 *
 * 注意：此函数为未来功能预留，当前系统主要使用固定的target值
 */
export const getMQTTTarget = (contentType: string, specificTarget?: string): string => {
	switch (contentType) {
		case "global":
			return MQTTTarget.ALL;
		case "targeted":
			// 如果指定了具体目标，使用指定目标；否则默认为大屏
			return specificTarget || MQTTTarget.SCREEN;
		default:
			// 兼容旧的contentType值，默认为全局
			return MQTTTarget.ALL;
	}
};

/**
 * MQTT 集成 Hook
 */
export function useMQTTIntegration(options: MQTTIntegrationOptions): MQTTIntegrationReturn {
	const { onLog, onMemoryStatsUpdate, getLogCount, onLogCleanup, raceApi, debug = process.env.NODE_ENV === 'development' } = options;

	// MQTT 配置
	const mqttConfig = useMemo(() => {
		const baseConfig = process.env.NODE_ENV === 'development'
			? MQTTPresets.development
			: MQTTPresets.production;

		return {
			...baseConfig,
			clientId: "Nexus-Panel"
		};
	}, []);

	// 使用基础 MQTT Hook
	const mqtt = useMQTT({
		config: mqttConfig,
		autoConnect: true,
		debug
	});

	// 使用 MQTT 内存管理器 Hook
	const memoryManager = useMQTTMemoryManager({
		config: {
			cleanupInterval: 30 * 60 * 1000, // 30分钟
			handlerThreshold: 50,
			logThreshold: 1000,
			handlerExpireTime: 60 * 60 * 1000, // 1小时
			debug
		},
		debug
	});

	// 状态管理
	const [memoryStats, setMemoryStats] = useState<MQTTMemoryStats | null>(null);

	// 引用管理
	const mqttSubscriptionRef = useRef<Set<string>>(new Set());
	const memoryManagerStartedRef = useRef(false);
	const prevConnectionStatusRef = useRef<boolean | null>(null);

	// 内存管理器初始化
	useEffect(() => {
		if (mqtt.isConnected && mqtt.service && !memoryManagerStartedRef.current) {
			// 启动内存管理器
			memoryManager.start(mqtt.service);
			memoryManagerStartedRef.current = true;

			// 设置回调函数
			memoryManager.setLogCountCallback(getLogCount);
			memoryManager.setLogCleanupCallback(onLogCleanup);

			onLog("info", "MQTT内存管理器已启动", {
				配置: {
					清理间隔: "30分钟",
					处理器阈值: 50,
					日志阈值: 1000
				}
			});
		} else if (!mqtt.isConnected && memoryManagerStartedRef.current) {
			// MQTT 断开时停止内存管理器
			memoryManager.stop();
			memoryManagerStartedRef.current = false;
		}
	}, [mqtt.isConnected, mqtt.service, memoryManager, onLog, getLogCount, onLogCleanup]);

	// 更新内存统计
	useEffect(() => {
		if (memoryManager.memoryStats) {
			setMemoryStats(memoryManager.memoryStats);
			onMemoryStatsUpdate();
		}
	}, [memoryManager.memoryStats, onMemoryStatsUpdate]);

	// MQTT 连接状态监听
	useEffect(() => {
		const currentStatus = mqtt.isConnected;
		const prevStatus = prevConnectionStatusRef.current;

		// 只在连接状态真正变化时记录日志
		if (prevStatus !== null && prevStatus !== currentStatus) {
			if (currentStatus) {
				// 从断开到连接的状态变化
				onLog("success", "MQTT连接成功", {
					broker: mqttConfig.brokerUrl,
					clientId: mqttConfig.clientId,
				});
			}
		}

		// 处理错误信息
		if (mqtt.error) {
			// 解析错误信息，提供更详细的错误描述
			let errorDetails = mqtt.error;
			let suggestions: string[] = [];

			if (mqtt.error.includes("Connection refused: Not authorized")) {
				errorDetails = "MQTT认证失败：设备可能被服务器拉黑";
				suggestions = [
					"设备因频繁重连被服务器加入黑名单",
					"请等待5-10分钟后再尝试连接",
					"或联系管理员从黑名单中移除设备",
					"已优化重连策略避免再次被拉黑"
				];
			} else if (mqtt.error.includes("ENOTFOUND")) {
				errorDetails = "MQTT服务器地址无法解析";
				suggestions = [
					"检查网络连接",
					"确认服务器地址是否正确",
					"尝试使用IP地址代替域名"
				];
			} else if (mqtt.error.includes("ECONNREFUSED")) {
				errorDetails = "MQTT服务器拒绝连接";
				suggestions = [
					"检查服务器是否运行",
					"确认端口号是否正确",
					"检查防火墙设置"
				];
			}

			onLog("error", errorDetails, {
				原始错误: mqtt.error,
				建议解决方案: suggestions,
				服务器配置: {
					地址: mqttConfig.brokerUrl,
					用户名: mqttConfig.username,
					客户端ID: mqttConfig.clientId
				}
			});
		}

		// 更新状态记录
		prevConnectionStatusRef.current = currentStatus;
	}, [mqtt.isConnected, mqtt.error, mqttConfig, onLog]);

	// MQTT 消息订阅监听
	useEffect(() => {
		const monitorTopic = "#";
		const subscriptionSet = mqttSubscriptionRef.current;

		if (mqtt.isConnected && !subscriptionSet.has(monitorTopic)) {
			// 订阅所有消息用于监控
			mqtt.subscribe(monitorTopic, (message: MQTTMessage, topic: string) => {
				// 过滤自己发送的消息，避免重复日志显示
				if (message.sender === mqttConfig.clientId) {
					return; // 不处理自己发送的消息
				}

				// 记录外部消息，使用接收标识
				onLog("get", `← 接收: ${topic}`, {
					...message.data,
					_type: "inbound",
					_from: message.sender
				});
			});

			// 记录已订阅的主题
			subscriptionSet.add(monitorTopic);
		}

		// 清理函数：连接断开时清理订阅状态
		return () => {
			if (!mqtt.isConnected) {
				subscriptionSet.clear();
			}
		};
	}, [mqtt.isConnected, mqtt, onLog, mqttConfig.clientId]);

	// 处理项目选择变化
	const handleProjectChange = useCallback((projectKey: string | null) => {
		if (projectKey && mqtt.isConnected) {
			let raceId: string | number;
			let raceName: string;

			// 记录开始处理项目选择
			onLog("info", `开始处理赛事选择: ${projectKey}`, {
				projectKey,
				timestamp: Date.now(),
				action: 'handle_project_change_start',
				source: 'mqtt_integration'
			});

			// 优先使用API数据查找赛事信息
			const race = raceApi.findRaceById(projectKey);
			if (race) {
				// 使用API返回的真实数据
				raceId = race.id; // 使用API中的"赛事 ID"字段
				raceName = race.name; // 使用API返回的真实名称

				onLog("success", `✓ 通过 API 找到赛事: ${race.name} (ID: ${race.id})`, {
					raceId: race.id,
					dbId: race.dbId,
					name: race.name,
					projectKey,
					timestamp: Date.now(),
					action: 'race_found_via_api',
					source: "api_data"
				});
			} else {
				// 备用方案：尝试使用静态映射
				// 检查projectKey是否为"project"前缀格式
				if (projectKey.startsWith("project")) {
					const numericId = parseInt(projectKey.replace("project", ""));
					if (!isNaN(numericId) && numericId < PROJECT_RACE_MAPPING.length) {
						raceId = numericId;
						raceName = PROJECT_RACE_MAPPING[numericId];

						onLog("warning", `使用静态映射查找赛事: ${raceName} (ID: ${numericId})`, {
							projectKey,
							raceId: numericId,
							raceName,
							source: "static_mapping"
						});
					} else {
						onLog("error", `无效的项目ID格式: ${projectKey}`, {
							projectKey,
							reason: "invalid_numeric_id"
						});
						return;
					}
				} else {
					// 无法识别的格式
					onLog("error", `无法识别的项目ID格式: ${projectKey}，且API中未找到对应赛事`, {
						projectKey,
						reason: "unknown_format_and_not_found_in_api"
					});
					return;
				}
			}

			try {
				mqtt.publish(
					MQTTUtils.createTopic(
						MQTTDomain.SYSTEM,
						MQTTContext.RACE,
						MQTTTarget.ALL,
						MQTTAction.LOAD
					),
					{
						raceId,
						raceName,
						status: "active",
						timestamp: Date.now(),
					}
				);

				onLog("send", `→ 已发送: 赛事切换 - ${raceName}`, {
					topic: `${MQTTDomain.SYSTEM}/${MQTTContext.RACE}/${MQTTTarget.ALL}/${MQTTAction.LOAD}`,
					raceId,
					raceName,
					_type: "outbound"
				});
			} catch (error) {
				onLog("error", "发送赛事消息失败", {
					error: error instanceof Error ? error.message : String(error),
					raceId,
					raceName
				});
			}
		}
	}, [mqtt, onLog, raceApi]);

	// 导航 MQTT 消息处理函数
	const handleNavigationMQTTMessage = useCallback((navigationKey: string) => {
		try {
			if (navigationKey.startsWith("rules-")) {
				// 规则显示消息
				const ruleType = navigationKey.replace("rules-", "");
				const rule = NAVIGATION_MQTT_MAPPING.rules[ruleType as keyof typeof NAVIGATION_MQTT_MAPPING.rules];

				if (rule) {
					mqtt.publish(
						MQTTUtils.createTopic(
							MQTTDomain.DISPLAY,
							MQTTContext.RULE,
							MQTTTarget.SCREEN,
							MQTTAction.SHOW
						),
						{
							ruleType,
							ruleId: rule.id,
							ruleName: rule.name,
							content: `${rule.name}的详细说明...`,
						}
					);

					onLog("send", `→ 已发送: 规则显示 - ${rule.name}`, {
						topic: `${MQTTDomain.DISPLAY}/${MQTTContext.RULE}/${MQTTTarget.SCREEN}/${MQTTAction.SHOW}`,
						ruleType,
						ruleId: rule.id,
						ruleName: rule.name,
						_type: "outbound"
					});
				}
			} else if (navigationKey.startsWith("switch-")) {
				// 环节切换消息
				const sessionType = navigationKey.replace("switch-", "");
				const session = NAVIGATION_MQTT_MAPPING.sessions[sessionType as keyof typeof NAVIGATION_MQTT_MAPPING.sessions];

				if (session) {
					if (sessionType === "ranking") {
						// 排行榜显示
						mqtt.publish(
							MQTTUtils.createTopic(
								MQTTDomain.DISPLAY,
								MQTTContext.RANK,
								MQTTTarget.SCREEN,
								MQTTAction.SHOW
							),
							{ rankType: "general", timestamp: Date.now() }
						);

						onLog("send", "→ 已发送: 排行榜显示", {
							topic: `${MQTTDomain.DISPLAY}/${MQTTContext.RANK}/${MQTTTarget.SCREEN}/${MQTTAction.SHOW}`,
							rankType: "general",
							_type: "outbound"
						});
					} else {
						// 环节开始
						mqtt.publish(
							MQTTUtils.createTopic(
								MQTTDomain.QUIZ,
								MQTTContext.SESSION,
								MQTTTarget.ALL,
								MQTTAction.START
							),
							{
								sessionType,
								sessionId: session.id,
								sessionName: session.name,
								startTime: Date.now(),
								config: {},
							}
						);

						onLog("send", `→ 已发送: 环节开始 - ${session.name}`, {
							topic: `${MQTTDomain.QUIZ}/${MQTTContext.SESSION}/${MQTTTarget.ALL}/${MQTTAction.START}`,
							sessionType,
							sessionId: session.id,
							sessionName: session.name,
							_type: "outbound"
						});
					}
				}
			}
		} catch (error) {
			onLog("error", "发送导航消息失败", {
				navigationKey,
				error: error instanceof Error ? error.message : String(error),
			});
		}
	}, [mqtt, onLog]);

	// 处理导航选择变化
	const handleNavigationChange = useCallback((navigationKey: string | null) => {
		if (navigationKey && mqtt.isConnected) {
			handleNavigationMQTTMessage(navigationKey);
		}
	}, [mqtt.isConnected, handleNavigationMQTTMessage]);

	// 手动重连处理器
	const handleReconnect = useCallback(async (): Promise<void> => {
		try {
			onLog("info", "正在尝试重新连接MQTT服务器...", {
				说明: "使用智能重连策略，已重置重连计数"
			});
			await mqtt.reconnect();
			// 移除重复的连接成功日志，因为状态变化监听会自动处理
		} catch (error) {
			onLog("error", "手动重连失败", {
				error: error instanceof Error ? error.message : String(error),
				建议: "请等待几分钟后再尝试，避免被服务器拉黑"
			});
			throw error;
		}
	}, [mqtt, onLog]);

	return {
		connectionStatus: mqtt.connectionStatus,
		isConnected: mqtt.isConnected,
		error: mqtt.error,
		memoryStats,
		handleProjectChange,
		handleNavigationChange,
		handleReconnect,
	};
}
