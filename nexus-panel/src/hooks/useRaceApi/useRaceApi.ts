/**
 * 赛事数据专用 Hook - 重构后的主文件
 * 
 * 基于通用 useApi Hook，提供专门处理赛事数据的功能
 * 包括数据获取、状态管理、错误处理和日志记录
 */

import React, { useCallback, useMemo, useState } from 'react';
import { useApi, useApiImmediate } from '../useApi';
import {
    getVisibleRaces,
    checkRaceServiceHealth,
    getNavigationData,
    getGroupedConfigurationData,
    getSimpleRulesIntroductionData
} from '../../services/api';

// 导入模块化的组件
import { createFactories } from './factories';
import { createHelpers } from './helpers';
import { useCacheManager } from './cache';
import type {
    UseRaceApiOptions,
    UseRaceApiReturn,
    RaceApiHandlers,
    ProcessedRaceItem,
    ApiError,
    NavigationNode,
    GroupedConfigurationData,
    ProcessedRulesIntroductionItem,
    ProcessedQuestionItem,
    RankingData,
    RankingProgress,
    PlayerInfoApiItem
} from './types';

/**
 * 赛事数据 Hook
 * 
 * @param options - Hook 配置选项
 * @returns 赛事数据状态和控制方法
 */
export function useRaceApi(options: UseRaceApiOptions = {}): UseRaceApiReturn {
    const {
        immediate = true,
        onSuccess,
        onError,
        onLog,
        selectedProject,
        selectedNavigationKey,
        packageChangeDialog,
        setPackageChangeDialog,
    } = options;

    // 稳定化回调函数
    const callbacksRef = React.useRef({ onSuccess, onError, onLog });
    callbacksRef.current = { onSuccess, onError, onLog };

    // 包装 API 调用以添加日志记录
    const fetchRacesWithLogging = useCallback(async (): Promise<ProcessedRaceItem[]> => {
        try {
            // 记录开始获取数据
            callbacksRef.current.onLog?.('info', '正在通过 API 获取赛事数据...', {
                timestamp: Date.now(),
                action: 'fetch_races_start',
                source: 'race_api'
            });

            // 获取赛事数据
            const races = await getVisibleRaces();

            // 记录成功获取数据
            callbacksRef.current.onLog?.('success', `成功通过 API 获取 ${races.length} 个赛事`, {
                count: races.length,
                races: races.map(race => ({ id: race.id, name: race.name })),
                timestamp: Date.now(),
                action: 'fetch_races_success',
                source: 'race_api'
            });

            return races;

        } catch (error) {
            // 记录错误
            callbacksRef.current.onLog?.('error', '获取赛事数据失败', {
                error: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                action: 'fetch_races_error'
            });

            // 重新抛出错误
            throw error;
        }
    }, []); // 移除 onLog 依赖

    // 使用通用 API Hook
    const apiResult = useApi(fetchRacesWithLogging, {
        immediate,
        onSuccess: (races) => {
            callbacksRef.current.onSuccess?.(races);
        },
        onError: (error) => {
            callbacksRef.current.onError?.(error);
        },
    });

    // 创建缓存管理器
    const cacheManager = useCacheManager(callbacksRef);

    // 计算派生状态
    const races = useMemo(() => {
        return apiResult.data || [];
    }, [apiResult.data]);

    const hasData = useMemo(() => {
        return races.length > 0;
    }, [races.length]);

    const count = useMemo(() => races.length, [races.length]);

    // 创建工厂函数
    const factories = createFactories(callbacksRef);

    // 创建辅助函数
    const helpers = createHelpers(callbacksRef, races);

    // 导航数据状态管理
    const [navigationData, setNavigationData] = useState<NavigationNode[] | null>(null);
    const [navigationLoading, setNavigationLoading] = useState<boolean>(false);
    const [navigationError, setNavigationError] = useState<ApiError | null>(null);

    // 配置数据状态管理
    const [configurationData, setConfigurationData] = useState<GroupedConfigurationData | null>(null);
    const [configurationLoading, setConfigurationLoading] = useState<boolean>(false);
    const [configurationError, setConfigurationError] = useState<ApiError | null>(null);

    // 规则介绍数据状态管理
    const [rulesIntroductionData, setRulesIntroductionData] = useState<ProcessedRulesIntroductionItem[] | null>(null);
    const [rulesIntroductionLoading, setRulesIntroductionLoading] = useState<boolean>(false);
    const [rulesIntroductionError, setRulesIntroductionError] = useState<ApiError | null>(null);

    // 题目数据状态管理
    const [questionData, setQuestionData] = useState<ProcessedQuestionItem[] | null>(null);
    const [questionLoading, setQuestionLoading] = useState<boolean>(false);
    const [questionError, setQuestionError] = useState<ApiError | null>(null);
    const [currentStage, setCurrentStage] = useState<string | null>(null);
    const [currentPackage, setCurrentPackage] = useState<string | null>("1"); // 默认使用正式题包

    // 排名数据状态管理
    const [rankingData, setRankingData] = useState<RankingData | null>(null);
    const [rankingLoading, setRankingLoading] = useState<boolean>(false);
    const [rankingError, setRankingError] = useState<ApiError | null>(null);
    const [rankingProgress, setRankingProgress] = useState<RankingProgress | null>(null);

    // 环节排名数据状态管理 - 按环节名称分组存储
    const [sectionRankingDataMap, setSectionRankingDataMap] = useState<Map<string, RankingData>>(new Map());
    const [sectionRankingLoading, setSectionRankingLoading] = useState<boolean>(false);
    const [sectionRankingError, setSectionRankingError] = useState<ApiError | null>(null);
    const [sectionPollingInterval, setSectionPollingInterval] = useState<NodeJS.Timeout | null>(null);
    const [currentSectionName, setCurrentSectionName] = useState<string | null>(null);

    // 计算当前环节的排名数据
    const sectionRankingData = useMemo(() => {
        return currentSectionName ? sectionRankingDataMap.get(currentSectionName) || null : null;
    }, [sectionRankingDataMap, currentSectionName]);

    // 选手列表数据状态管理
    const [playerListData, setPlayerListData] = useState<PlayerInfoApiItem[] | null>(null);
    const [playerListLoading, setPlayerListLoading] = useState<boolean>(false);
    const [playerListError, setPlayerListError] = useState<ApiError | null>(null);

    // 合并数据就绪状态
    const [allDataReady, setAllDataReady] = useState<boolean>(false);

    // 计算合并数据就绪状态
    React.useEffect(() => {
        const isReady = navigationData !== null &&
            configurationData !== null &&
            !navigationLoading &&
            !configurationLoading &&
            !navigationError &&
            !configurationError;

        setAllDataReady(isReady);
    }, [navigationData, configurationData, navigationLoading, configurationLoading, navigationError, configurationError]);

    // 根据 ID 查找赛事
    const findRaceById = useCallback((id: string): ProcessedRaceItem | undefined => {
        return races.find(race => race.id === id);
    }, [races]);

    // 检查服务健康状态
    const checkHealth = useCallback(async (): Promise<boolean> => {
        try {
            callbacksRef.current.onLog?.('info', '正在检查赛事服务健康状态...', {
                timestamp: Date.now(),
                action: 'health_check_start'
            });

            const isHealthy = await checkRaceServiceHealth();

            callbacksRef.current.onLog?.(isHealthy ? 'success' : 'warning',
                isHealthy ? '赛事服务运行正常' : '赛事服务不可用', {
                healthy: isHealthy,
                timestamp: Date.now(),
                action: 'health_check_result'
            });

            return isHealthy;

        } catch (error) {
            callbacksRef.current.onLog?.('error', '健康检查失败', {
                error: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                action: 'health_check_error'
            });

            return false;
        }
    }, []);

    // 使用工厂函数创建重置函数
    const resetNavigationData = useCallback(() => {
        factories.createResetFunction('导航', setNavigationData, setNavigationLoading, setNavigationError)();
    }, [factories]);

    const resetConfigurationData = useCallback(() => {
        factories.createResetFunction('配置', setConfigurationData, setConfigurationLoading, setConfigurationError)();
    }, [factories]);

    const resetRulesIntroductionData = useCallback(() => {
        factories.createResetFunction('规则介绍', setRulesIntroductionData, setRulesIntroductionLoading, setRulesIntroductionError)();
    }, [factories]);

    const resetPlayerListData = useCallback(() => {
        factories.createResetFunction('选手列表', setPlayerListData, setPlayerListLoading, setPlayerListError)();
    }, [factories]);

    const resetQuestionData = useCallback(() => {
        factories.createExtendedResetFunction(
            '题目',
            setQuestionData,
            setQuestionLoading,
            setQuestionError,
            () => setCurrentStage(null) // 额外的重置操作
        )();
    }, [factories]);

    const resetRankingData = useCallback(() => {
        factories.createExtendedResetFunction(
            '排名',
            setRankingData,
            setRankingLoading,
            setRankingError,
            () => {
                // 额外的重置操作：清理进度状态和内存
                setRankingProgress(null);

                // 强制垃圾回收提示（在开发环境中）
                if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined' && 'gc' in window) {
                    try {
                        (window as typeof window & { gc?: () => void }).gc?.();
                    } catch {
                        // 忽略垃圾回收错误
                    }
                }
            }
        )();
    }, [factories]);

    const resetSectionRankingData = useCallback((sectionName?: string) => {
        if (sectionName) {
            // 重置特定环节的数据
            setSectionRankingDataMap(prev => {
                const newMap = new Map(prev);
                newMap.delete(sectionName);
                return newMap;
            });
        } else {
            // 重置所有环节数据
            setSectionRankingDataMap(new Map());
        }

        setSectionRankingLoading(false);
        setSectionRankingError(null);

        // 停止轮询
        if (sectionPollingInterval) {
            clearTimeout(sectionPollingInterval);
            setSectionPollingInterval(null);
        }
    }, [sectionPollingInterval]);

    // 切换当前环节的函数
    const switchToSection = useCallback((sectionName: string) => {
        setCurrentSectionName(sectionName);
    }, []);

    // 使用工厂函数创建数据获取函数
    const fetchNavigationData = useCallback(
        (baseId: string) => factories.createDataFetcher(
            '导航',
            setNavigationData,
            setNavigationLoading,
            setNavigationError,
            getNavigationData
        )(baseId),
        [factories]
    );

    const fetchConfigurationData = useCallback(
        (baseId: string) => factories.createTableBasedDataFetcher(
            '配置信息',
            '赛事素材表', // 使用实际的数据库表名
            setConfigurationData,
            setConfigurationLoading,
            setConfigurationError,
            getGroupedConfigurationData,
            cacheManager.getTableStructureWithCache
        )(baseId),
        [factories, cacheManager]
    );

    const fetchRulesIntroductionData = useCallback(
        (baseId: string) => factories.createTableBasedDataFetcher(
            '规则介绍',
            '赛事素材表', // 使用实际的数据库表名
            setRulesIntroductionData,
            setRulesIntroductionLoading,
            setRulesIntroductionError,
            getSimpleRulesIntroductionData,
            cacheManager.getTableStructureWithCache
        )(baseId),
        [factories, cacheManager]
    );

    // 根据标题查找规则介绍项
    const findRulesIntroductionByTitle = useCallback((title: string): ProcessedRulesIntroductionItem | undefined => {
        return rulesIntroductionData?.find(item => item.title === title);
    }, [rulesIntroductionData]);

    // 根据题号查找题目
    const findQuestionByNumber = useCallback((questionNumber: number): ProcessedQuestionItem | undefined => {
        return questionData?.find(item => item.questionNumber === questionNumber);
    }, [questionData]);

    // 重置所有项目数据
    const resetAllProjectData = useCallback((): void => {
        // 使用已重构的重置函数
        resetNavigationData();
        resetConfigurationData();
        resetRulesIntroductionData();
        resetQuestionData();
        resetPlayerListData();

        // 重置合并数据状态
        setAllDataReady(false);

        // 清理表结构缓存和当前表结构数据
        cacheManager.clearTableStructureCache();
        cacheManager.setCurrentTableStructure(null);

        callbacksRef.current.onLog?.('info', '所有项目数据已重置', {
            timestamp: Date.now(),
            action: 'reset_all_project_data'
        });
    }, [
        resetNavigationData,
        resetConfigurationData,
        resetRulesIntroductionData,
        resetQuestionData,
        resetPlayerListData,
        cacheManager
    ]);

    // 合并数据获取方法 - 复用现有函数避免重复请求
    const fetchAllProjectData = useCallback(async (baseId: string): Promise<void> => {
        try {
            // 添加函数开始调试
            callbacksRef.current.onLog?.('info', '🎯 fetchAllProjectData 函数开始执行', {
                baseId,
                timestamp: Date.now(),
                action: 'fetch_all_project_data_function_start'
            });

            // 设置所有加载状态
            setNavigationLoading(true);
            setConfigurationLoading(true);
            setNavigationError(null);
            setConfigurationError(null);
            setAllDataReady(false);

            callbacksRef.current.onLog?.('info', '🚀 开始获取项目完整数据（复用现有函数避免重复请求）', {
                baseId,
                timestamp: Date.now(),
                action: 'fetch_all_project_data_start',
                optimization: 'reuse_existing_functions'
            });

            // 验证项目数据
            helpers.validateProjectData(baseId);

            callbacksRef.current.onLog?.('info', '🔄 开始并行获取导航数据和配置数据', {
                baseId,
                timestamp: Date.now(),
                action: 'parallel_data_fetch_start'
            });

            // 使用简化的数据服务获取所有数据
            const { createSimplifiedDataService } = await import('../../services/api/simplifiedDataService');
            const dataService = createSimplifiedDataService(cacheManager);
            const dataResult = await dataService.fetchAllProjectData(baseId);

            // 检查获取结果
            if (!dataResult.success) {
                const errorMessage = `数据获取失败: ${dataResult.errors.join(', ')}`;
                throw new Error(errorMessage);
            }

            // 记录警告信息
            if (dataResult.warnings.length > 0) {
                console.warn('⚠️ 数据获取过程中的警告', {
                    baseId,
                    warnings: dataResult.warnings,
                    timestamp: Date.now()
                });
            }

            const navigationResult = dataResult.navigationData;
            const configurationResult = dataResult.configurationData;

            callbacksRef.current.onLog?.('info', '✅ 并行数据获取完成', {
                baseId,
                navigationResultExists: !!navigationResult,
                configurationResultExists: !!configurationResult,
                navigationResultLength: navigationResult?.length || 0,
                timestamp: Date.now(),
                action: 'parallel_data_fetch_complete'
            });

            // 调试：检查获取到的数据
            callbacksRef.current.onLog?.('info', '🔍 fetchAllProjectData 数据获取结果调试', {
                navigationResultExists: !!navigationResult,
                navigationResultLength: navigationResult?.length || 0,
                configurationResultExists: !!configurationResult,
                navigationResultSample: navigationResult?.slice(0, 3).map(n => ({ id: n.id, name: n.name })),
                baseId,
                timestamp: Date.now(),
                action: 'fetch_all_project_data_debug'
            });

            // 更新状态前的调试
            console.log('🔧 useRaceApi 准备更新导航数据状态', {
                navigationResultLength: navigationResult.length,
                navigationResultSample: navigationResult.slice(0, 3).map(n => ({ id: n.id, name: n.name, contentType: n.contentType })),
                timestamp: Date.now(),
                action: 'before_set_navigation_data'
            });

            // 更新状态
            setNavigationData(navigationResult);
            setConfigurationData(configurationResult);
            setAllDataReady(true);

            // 更新状态后的调试
            console.log('✅ useRaceApi 导航数据状态更新完成', {
                navigationResultLength: navigationResult.length,
                timestamp: Date.now(),
                action: 'after_set_navigation_data'
            });

            // 调试：确认状态更新
            callbacksRef.current.onLog?.('info', '🔄 fetchAllProjectData 状态更新完成', {
                navigationDataUpdated: true,
                configurationDataUpdated: true,
                allDataReadySet: true,
                baseId,
                timestamp: Date.now(),
                action: 'fetch_all_project_data_state_updated'
            });

            callbacksRef.current.onLog?.('success', '✅ 项目完整数据获取成功（避免重复环节数据请求）', {
                navigationNodesCount: navigationResult.length,
                leaderCount: configurationResult.leaderDisplay.length,
                playerCount: configurationResult.playerDisplay.length,
                awardCount: configurationResult.awardDisplay.length,
                baseId,
                timestamp: Date.now(),
                action: 'fetch_all_project_data_success',
                optimization: 'no_duplicate_section_requests'
            });

        } catch (error) {
            // 添加详细的错误调试
            callbacksRef.current.onLog?.('error', '❌ fetchAllProjectData 执行出错', {
                error: error instanceof Error ? error.message : String(error),
                errorStack: error instanceof Error ? error.stack : undefined,
                baseId,
                timestamp: Date.now(),
                action: 'fetch_all_project_data_error'
            });

            const apiError = factories.handleApiError(error, '获取项目完整数据', baseId);

            // 设置错误状态
            setNavigationError(apiError);
            setConfigurationError(apiError);
            setAllDataReady(false);

            throw apiError;
        } finally {
            setNavigationLoading(false);
            setConfigurationLoading(false);
        }
    }, [helpers, cacheManager, factories]);

    // 题目数据获取函数
    const fetchQuestionData = useCallback(
        async (baseId: string, sectionName: string, packageNumber: string = "1", stage?: string): Promise<void> => {
            try {
                setQuestionLoading(true);
                setQuestionError(null);

                callbacksRef.current.onLog?.('info', '正在获取题目数据...', {
                    baseId,
                    sectionName,
                    packageNumber,
                    stage,
                    timestamp: Date.now(),
                    action: 'fetch_question_data_start'
                });

                const tableStructureData = await cacheManager.getTableStructureWithCache(baseId);
                const tables = tableStructureData.list;

                const questionTable = tables.find(table => table.title === "题目表");
                if (!questionTable) {
                    throw new Error('未找到题目表');
                }

                const { getQuestionData } = await import('../../services/api');
                const result = await getQuestionData(questionTable.id, sectionName, packageNumber, stage);

                setQuestionData(result);
                if (stage) {
                    setCurrentStage(stage);
                }
                setCurrentPackage(packageNumber);

                callbacksRef.current.onLog?.('success', '题目数据获取成功', {
                    baseId,
                    sectionName,
                    packageNumber,
                    stage,
                    count: result.length,
                    timestamp: Date.now(),
                    action: 'fetch_question_data_success'
                });
            } catch (error) {
                const apiError = factories.handleApiError(error, '获取题目数据', baseId, { sectionName, packageNumber, stage });
                setQuestionError(apiError);
                throw apiError;
            } finally {
                setQuestionLoading(false);
            }
        },
        [factories, cacheManager]
    );

    const fetchQuestionDataWithStage = useCallback(
        async (baseId: string, sectionName: string, stage: string, packageNumber: string = "1"): Promise<void> => {
            return fetchQuestionData(baseId, sectionName, packageNumber, stage);
        },
        [fetchQuestionData]
    );

    // 排名数据获取函数 - 优化版本，使用缓存避免重复请求
    const fetchRankingData = useCallback(
        async (baseId: string): Promise<void> => {
            try {
                setRankingLoading(true);
                setRankingError(null);
                setRankingProgress({ stage: 'table_structure', progress: 10, message: '准备获取排名数据（使用缓存优化）...' });

                callbacksRef.current.onLog?.('info', '正在获取排名数据（使用表结构缓存）...', {
                    baseId,
                    optimization: 'use_table_structure_cache',
                    timestamp: Date.now(),
                    action: 'fetch_ranking_data_start_optimized'
                });

                const { getRankingData } = await import('../../services/api');

                // 使用缓存的表结构获取函数，避免重复请求bases API
                const result = await getRankingData(
                    baseId,
                    undefined, // onProgress 参数
                    cacheManager.getTableStructureWithCache
                );

                setRankingData(result);
                setRankingProgress({ stage: 'complete', progress: 100, message: '排名数据获取完成（已优化）' });

                callbacksRef.current.onLog?.('success', '排名数据获取成功（已优化重复请求）', {
                    baseId,
                    optimization: 'table_structure_cache_used',
                    timestamp: Date.now(),
                    action: 'fetch_ranking_data_success_optimized'
                });
            } catch (error) {
                const apiError = factories.handleApiError(error, '获取排名数据', baseId);
                setRankingError(apiError);
                setRankingProgress({ stage: 'complete', progress: 0, message: '排名数据获取失败' });
                throw apiError;
            } finally {
                setRankingLoading(false);
            }
        },
        [factories, cacheManager]
    );

    // 环节排名数据获取函数 - 优化版本，使用缓存避免重复请求
    const fetchSectionRankingData = useCallback(
        async (baseId: string, sectionName: string): Promise<void> => {
            // 强制日志 - 确认函数被调用
            console.log(`[fetchSectionRankingData] 函数开始执行`, {
                baseId,
                sectionName,
                timestamp: Date.now(),
                action: 'function_start'
            });

            try {
                setSectionRankingLoading(true);
                setSectionRankingError(null);

                callbacksRef.current.onLog?.('info', '正在获取环节排名数据（使用表结构缓存）...', {
                    baseId,
                    sectionName,
                    optimization: 'use_table_structure_cache',
                    timestamp: Date.now(),
                    action: 'fetch_section_ranking_data_start_optimized'
                });

                const { getSectionRankingData } = await import('../../services/api');

                // 使用缓存的表结构获取函数，避免重复请求bases API
                const result = await getSectionRankingData(
                    baseId,
                    sectionName,
                    cacheManager.getTableStructureWithCache
                );

                // 设置当前环节名称并将数据存储到Map中，添加获取时间戳
                setCurrentSectionName(sectionName);
                setSectionRankingDataMap(prev => {
                    const newMap = new Map(prev);
                    // 为数据添加获取时间戳，用于跟踪真正的API更新
                    const dataWithTimestamp = {
                        ...result,
                        fetchTimestamp: Date.now()
                    };
                    newMap.set(sectionName, dataWithTimestamp);
                    return newMap;
                });

                callbacksRef.current.onLog?.('success', '环节排名数据获取成功（已优化重复请求）', {
                    baseId,
                    sectionName,
                    optimization: 'table_structure_cache_used',
                    timestamp: Date.now(),
                    action: 'fetch_section_ranking_data_success_optimized'
                });
            } catch (error) {
                const apiError = factories.handleApiError(error, '获取环节排名数据', baseId, { sectionName });
                setSectionRankingError(apiError);
                throw apiError;
            } finally {
                setSectionRankingLoading(false);
            }
        },
        [factories, cacheManager]
    );

    // 开始环节排名轮询
    const startSectionRankingPolling = useCallback(
        (baseId: string, sectionName: string, interval: number = 5000): void => {
            // 停止现有轮询
            if (sectionPollingInterval) {
                clearTimeout(sectionPollingInterval);
            }

            const poll = async () => {
                try {
                    await fetchSectionRankingData(baseId, sectionName);
                    const timeoutId = setTimeout(poll, interval);
                    setSectionPollingInterval(timeoutId);
                } catch (error) {
                    callbacksRef.current.onLog?.('error', '环节排名轮询失败', {
                        baseId,
                        sectionName,
                        error: error instanceof Error ? error.message : String(error),
                        timestamp: Date.now(),
                        action: 'section_ranking_polling_error'
                    });
                }
            };

            poll();
        },
        [fetchSectionRankingData, sectionPollingInterval]
    );

    // 停止环节排名轮询
    const stopSectionRankingPolling = useCallback((): void => {
        if (sectionPollingInterval) {
            clearTimeout(sectionPollingInterval);
            setSectionPollingInterval(null);
            callbacksRef.current.onLog?.('info', '环节排名轮询已停止', {
                timestamp: Date.now(),
                action: 'section_ranking_polling_stopped'
            });
        }
    }, [sectionPollingInterval]);

    // 选手列表数据获取函数
    const fetchPlayerListData = useCallback(
        (baseId: string) => factories.createTableBasedDataFetcher(
            '选手列表',
            '选手信息表',
            setPlayerListData,
            setPlayerListLoading,
            setPlayerListError,
            async (tableId: string) => {
                const { getPlayerInfo } = await import('../../services/api');
                const response = await getPlayerInfo(tableId);
                return response.data.list;
            },
            cacheManager.getTableStructureWithCache
        )(baseId),
        [factories, cacheManager]
    );

    // 事件处理器
    const handlePackageChange = useCallback((packageId: string, packageName: string): void => {
        if (setPackageChangeDialog) {
            setPackageChangeDialog({
                isOpen: true,
                packageId,
                packageName
            });
        }
    }, [setPackageChangeDialog]);

    const handlePackageChangeConfirm = useCallback((): void => {
        if (packageChangeDialog && selectedProject && selectedNavigationKey) {
            const { packageId, packageName } = packageChangeDialog;

            // 更新当前题包状态
            setCurrentPackage(packageId);

            // 记录题包切换开始
            callbacksRef.current.onLog?.('info', `开始切换到题包: ${packageName}`, {
                packageId,
                packageName,
                selectedProject,
                selectedNavigationKey,
                timestamp: Date.now(),
                action: 'package_change_start'
            });

            // 获取当前导航节点信息
            const currentNode = navigationData?.find(node => node.id === selectedNavigationKey);
            if (!currentNode) {
                callbacksRef.current.onLog?.('error', '无法切换题包：未找到当前导航节点', {
                    selectedNavigationKey,
                    packageId,
                    packageName,
                    timestamp: Date.now(),
                    action: 'package_change_node_not_found'
                });
                return;
            }

            // 执行题包切换的数据获取逻辑
            const fetchPromise = currentStage && selectedProject
                ? fetchQuestionDataWithStage(selectedProject, currentNode.name, currentStage, packageId)
                : selectedProject
                    ? fetchQuestionData(selectedProject, currentNode.name, packageId)
                    : Promise.reject(new Error('No selected project'));

            fetchPromise
                .then(() => {
                    callbacksRef.current.onLog?.('success', `题包切换成功: ${packageName}`, {
                        packageId,
                        packageName,
                        sectionName: currentNode.name,
                        currentStage,
                        questionCount: questionData?.length || 0,
                        timestamp: Date.now(),
                        action: 'package_change_success'
                    });
                })
                .catch((error) => {
                    callbacksRef.current.onLog?.('error', `题包切换失败: ${packageName}`, {
                        packageId,
                        packageName,
                        sectionName: currentNode.name,
                        currentStage,
                        error: error instanceof Error ? error.message : String(error),
                        timestamp: Date.now(),
                        action: 'package_change_error'
                    });
                    // 切换失败时重置题包状态
                    setCurrentPackage("1");
                });
        }

        // 关闭对话框
        if (setPackageChangeDialog) {
            setPackageChangeDialog({ isOpen: false, packageId: '', packageName: '' });
        }
    }, [packageChangeDialog, selectedProject, selectedNavigationKey, setPackageChangeDialog, navigationData, currentStage, fetchQuestionDataWithStage, fetchQuestionData, questionData]);

    const handlePackageChangeCancel = useCallback((): void => {
        if (setPackageChangeDialog) {
            setPackageChangeDialog({ isOpen: false, packageId: '', packageName: '' });
        }
    }, [setPackageChangeDialog]);

    const handleStageChange = useCallback((stageName: string): void => {
        setCurrentStage(stageName);
        callbacksRef.current.onLog?.('info', '阶段已切换', {
            stageName,
            timestamp: Date.now(),
            action: 'stage_changed'
        });

        // 阶段切换后，如果有选中的项目和导航节点，重新获取对应阶段的题目数据
        if (selectedProject && selectedNavigationKey) {
            // 在树形结构中查找节点（包括子节点）
            let currentNode: NavigationNode | undefined;

            // 首先在顶级节点中查找
            currentNode = navigationData?.find(node => node.id === selectedNavigationKey);

            // 如果在顶级节点中没找到，则在子节点中查找
            if (!currentNode && navigationData) {
                for (const parentNode of navigationData) {
                    if (parentNode.children) {
                        currentNode = parentNode.children.find(child => child.id === selectedNavigationKey);
                        if (currentNode) break;
                    }
                }
            }

            if (currentNode && currentNode.contentType === '题目') {
                callbacksRef.current.onLog?.('info', '阶段切换后重新获取题目数据', {
                    stageName,
                    sectionName: currentNode.name,
                    selectedProject,
                    currentPackage,
                    timestamp: Date.now(),
                    action: 'stage_change_refetch_questions'
                });

                // 使用当前题包和新阶段重新获取题目数据
                fetchQuestionDataWithStage(selectedProject, currentNode.name, stageName, currentPackage || "1")
                    .then(() => {
                        callbacksRef.current.onLog?.('success', '阶段切换后题目数据获取成功', {
                            stageName,
                            sectionName: currentNode.name,
                            questionCount: questionData?.length || 0,
                            timestamp: Date.now(),
                            action: 'stage_change_refetch_success'
                        });
                    })
                    .catch((error) => {
                        callbacksRef.current.onLog?.('error', '阶段切换后题目数据获取失败', {
                            stageName,
                            sectionName: currentNode.name,
                            error: error instanceof Error ? error.message : String(error),
                            timestamp: Date.now(),
                            action: 'stage_change_refetch_error'
                        });
                    });
            }
        }
    }, [selectedProject, selectedNavigationKey, navigationData, currentPackage, fetchQuestionDataWithStage, questionData]);

    const handlers: RaceApiHandlers = {
        handlePackageChange,
        handlePackageChangeConfirm,
        handlePackageChangeCancel,
        handleStageChange
    };
    return {
        races,
        loading: apiResult.loading,
        error: apiResult.error,
        hasData,
        count,
        refetch: apiResult.refetch,
        reset: apiResult.reset,
        findRaceById,
        checkHealth,

        // 导航相关功能
        navigationData,
        navigationLoading,
        navigationError,
        fetchNavigationData,
        resetNavigationData,

        // 配置信息相关功能
        configurationData,
        configurationLoading,
        configurationError,
        fetchConfigurationData,
        resetConfigurationData,

        // 规则介绍相关功能
        rulesIntroductionData,
        rulesIntroductionLoading,
        rulesIntroductionError,
        fetchRulesIntroductionData,
        resetRulesIntroductionData,
        findRulesIntroductionByTitle,

        // 题目相关功能
        questionData,
        questionLoading,
        questionError,
        currentStage,
        currentPackage,
        fetchQuestionData,
        fetchQuestionDataWithStage,
        resetQuestionData,
        findQuestionByNumber,
        setCurrentStage,
        setCurrentPackage,

        // 合并数据功能
        allDataReady,
        fetchAllProjectData,
        resetAllProjectData,

        // 表结构缓存相关功能
        cachePerformanceStats: cacheManager.getCachePerformanceStats(),
        clearTableStructureCache: cacheManager.clearTableStructureCache,
        currentTableStructure: cacheManager.currentTableStructure,

        // 排名数据相关功能
        rankingData,
        rankingLoading,
        rankingError,
        rankingProgress,
        fetchRankingData,
        resetRankingData,

        // 环节排名数据相关功能
        sectionRankingData,
        sectionRankingLoading,
        sectionRankingError,
        fetchSectionRankingData,
        startSectionRankingPolling,
        stopSectionRankingPolling,
        resetSectionRankingData,
        switchToSection,
        currentSectionName,

        // 选手列表数据相关功能
        playerListData,
        playerListLoading,
        playerListError,
        fetchPlayerListData,
        resetPlayerListData,

        // 事件处理器
        handlers
    };
}

// ==================== 便捷 Hook 变体 ====================

/**
 * 自动获取赛事数据的 Hook
 * 组件挂载时自动获取数据
 */
export function useRaceApiImmediate(
    options: Omit<UseRaceApiOptions, 'immediate'> = {}
): UseRaceApiReturn {
    return useRaceApi({ ...options, immediate: true });
}

/**
 * 手动获取赛事数据的 Hook
 * 需要手动调用 refetch 方法获取数据
 */
export function useRaceApiManual(
    options: Omit<UseRaceApiOptions, 'immediate'> = {}
): UseRaceApiReturn {
    return useRaceApi({ ...options, immediate: false });
}

/**
 * 带状态监控的赛事数据 Hook
 * 自动监控服务健康状态
 */
export function useRaceApiWithHealthCheck(
    options: UseRaceApiOptions = {},
    healthCheckInterval: number = 30000 // 30秒检查一次
): UseRaceApiReturn & { serviceHealthy: boolean | null } {
    const raceApi = useRaceApi(options);

    // 使用独立的 API Hook 进行健康检查
    const healthCheckApi = useApiImmediate(
        () => checkRaceServiceHealth(),
        {
            onSuccess: (isHealthy) => {
                options.onLog?.(isHealthy ? 'success' : 'warning',
                    isHealthy ? '定期健康检查：服务正常' : '定期健康检查：服务异常', {
                    healthy: isHealthy,
                    timestamp: Date.now(),
                    action: 'periodic_health_check'
                });
            },
            onError: (error) => {
                options.onLog?.('error', '定期健康检查失败', {
                    error: error.message,
                    timestamp: Date.now(),
                    action: 'periodic_health_check_error'
                });
            }
        }
    );

    // 定期健康检查
    React.useEffect(() => {
        if (healthCheckInterval <= 0) return;

        const intervalId = setInterval(() => {
            healthCheckApi.refetch();
        }, healthCheckInterval);

        return () => clearInterval(intervalId);
    }, [healthCheckInterval, healthCheckApi]);

    return {
        ...raceApi,
        serviceHealthy: healthCheckApi.data,
    };
}