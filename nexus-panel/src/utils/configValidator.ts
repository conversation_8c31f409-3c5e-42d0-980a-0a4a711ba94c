/**
 * 配置验证工具
 * 用于验证数据库配置和表结构的一致性
 */

import { TABLE_MAPPINGS, type TableMapping } from '../config/tableMapping';

export interface ValidationResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface TableInfo {
  title: string;
  id: string;
  description?: string;
}

/**
 * 配置验证器
 */
export class ConfigValidator {
  /**
   * 验证项目配置
   * @param baseId 项目ID
   * @param tables 表结构列表
   * @returns 验证结果
   */
  static validateProjectConfig(baseId: string, tables: TableInfo[]): ValidationResult {
    const result: ValidationResult = {
      success: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    console.log('🔍 ConfigValidator 开始验证项目配置', {
      baseId,
      tablesCount: tables.length,
      timestamp: Date.now()
    });

    // 1. 验证必需的表
    this.validateRequiredTables(tables, result);

    // 2. 检查表名映射
    this.validateTableMappings(tables, result);

    // 3. 检查潜在的配置问题
    this.checkPotentialIssues(tables, result);

    // 4. 提供优化建议
    this.provideSuggestions(tables, result);

    result.success = result.errors.length === 0;

    console.log('✅ ConfigValidator 配置验证完成', {
      baseId,
      success: result.success,
      errorsCount: result.errors.length,
      warningsCount: result.warnings.length,
      suggestionsCount: result.suggestions.length,
      timestamp: Date.now()
    });

    return result;
  }

  /**
   * 验证必需的表是否存在
   */
  private static validateRequiredTables(tables: TableInfo[], result: ValidationResult): void {
    const requiredLogicalNames = ['NAVIGATION', 'CONFIGURATION'] as const;
    const availableTableNames = tables.map(t => t.title);

    for (const logicalName of requiredLogicalNames) {
      const mapping = TABLE_MAPPINGS[logicalName];
      if (!mapping) {
        result.errors.push(`表映射配置缺失: ${logicalName}`);
        continue;
      }

      const actualTableExists = availableTableNames.includes(mapping.actualName);
      if (!actualTableExists) {
        result.errors.push(
          `必需的表不存在: "${mapping.displayName}" (实际名称: "${mapping.actualName}")`
        );
        
        // 尝试找到相似的表名
        const similarTables = this.findSimilarTableNames(mapping.actualName, availableTableNames);
        if (similarTables.length > 0) {
          result.suggestions.push(
            `可能的替代表名: ${similarTables.join(', ')}`
          );
        }
      }
    }
  }

  /**
   * 验证表名映射的一致性
   */
  private static validateTableMappings(tables: TableInfo[], result: ValidationResult): void {
    const availableTableNames = tables.map(t => t.title);

    for (const [logicalName, mapping] of Object.entries(TABLE_MAPPINGS)) {
      // 检查实际表名是否存在
      if (!availableTableNames.includes(mapping.actualName)) {
        // 检查是否使用了显示名称而不是实际名称
        if (availableTableNames.includes(mapping.displayName)) {
          result.warnings.push(
            `表 "${logicalName}" 的配置可能有误：数据库中存在 "${mapping.displayName}" 但配置的实际名称是 "${mapping.actualName}"`
          );
          result.suggestions.push(
            `考虑将 ${logicalName} 的 actualName 更新为 "${mapping.displayName}"`
          );
        }
      }
    }
  }

  /**
   * 检查潜在的配置问题
   */
  private static checkPotentialIssues(tables: TableInfo[], result: ValidationResult): void {
    const availableTableNames = tables.map(t => t.title);

    // 检查是否有未映射的表
    const mappedTableNames = Object.values(TABLE_MAPPINGS).map(m => m.actualName);
    const unmappedTables = availableTableNames.filter(name => !mappedTableNames.includes(name));

    if (unmappedTables.length > 0) {
      result.warnings.push(
        `发现未映射的表: ${unmappedTables.join(', ')}`
      );
      result.suggestions.push(
        '考虑为这些表添加映射配置，以便在代码中使用'
      );
    }

    // 检查表名的命名规范
    const irregularNames = availableTableNames.filter(name => 
      name.includes(' ') || name.includes('-') || /[A-Z]/.test(name)
    );

    if (irregularNames.length > 0) {
      result.warnings.push(
        `发现可能不规范的表名: ${irregularNames.join(', ')}`
      );
      result.suggestions.push(
        '建议使用统一的表名命名规范（如：下划线分隔的小写字母）'
      );
    }
  }

  /**
   * 提供优化建议
   */
  private static provideSuggestions(tables: TableInfo[], result: ValidationResult): void {
    // 建议添加表描述
    const tablesWithoutDescription = tables.filter(t => !t.description);
    if (tablesWithoutDescription.length > 0) {
      result.suggestions.push(
        '建议为所有表添加描述信息，便于维护和理解'
      );
    }

    // 建议定期验证配置
    result.suggestions.push(
      '建议在部署前运行配置验证，确保数据库结构与代码配置一致'
    );
  }

  /**
   * 查找相似的表名
   */
  private static findSimilarTableNames(targetName: string, availableNames: string[]): string[] {
    const target = targetName.toLowerCase();
    return availableNames.filter(name => {
      const nameLower = name.toLowerCase();
      // 简单的相似度检查：包含相同的关键词
      const targetWords = target.split(/[\s\-_]+/);
      const nameWords = nameLower.split(/[\s\-_]+/);
      
      return targetWords.some(word => 
        nameWords.some(nameWord => 
          nameWord.includes(word) || word.includes(nameWord)
        )
      );
    });
  }

  /**
   * 生成配置报告
   */
  static generateConfigReport(baseId: string, tables: TableInfo[]): string {
    const validation = this.validateProjectConfig(baseId, tables);
    
    let report = `# 项目配置验证报告\n\n`;
    report += `**项目ID**: ${baseId}\n`;
    report += `**验证时间**: ${new Date().toLocaleString()}\n`;
    report += `**验证结果**: ${validation.success ? '✅ 通过' : '❌ 失败'}\n\n`;

    if (validation.errors.length > 0) {
      report += `## ❌ 错误 (${validation.errors.length})\n\n`;
      validation.errors.forEach((error, index) => {
        report += `${index + 1}. ${error}\n`;
      });
      report += '\n';
    }

    if (validation.warnings.length > 0) {
      report += `## ⚠️ 警告 (${validation.warnings.length})\n\n`;
      validation.warnings.forEach((warning, index) => {
        report += `${index + 1}. ${warning}\n`;
      });
      report += '\n';
    }

    if (validation.suggestions.length > 0) {
      report += `## 💡 建议 (${validation.suggestions.length})\n\n`;
      validation.suggestions.forEach((suggestion, index) => {
        report += `${index + 1}. ${suggestion}\n`;
      });
      report += '\n';
    }

    report += `## 📊 表结构概览\n\n`;
    report += `**总表数**: ${tables.length}\n\n`;
    
    report += `### 可用的表\n\n`;
    tables.forEach((table, index) => {
      report += `${index + 1}. **${table.title}** (ID: ${table.id})\n`;
    });

    return report;
  }
}
