/**
 * API请求调试脚本
 *
 * 在浏览器控制台中运行此脚本来监控和调试API请求
 *
 * 注意：这是开发调试工具，包含console输出用于调试目的
 * 仅在开发环境中使用，生产环境请移除或禁用
 */

(function () {
  'use strict';

  console.log('🔍 API请求调试脚本已启动');

  // 监控网络请求
  const originalFetch = window.fetch;
  const requestLog = [];

  window.fetch = function (...args) {
    const url = args[0];
    const timestamp = Date.now();

    // 检查是否是表结构API请求
    if (
      typeof url === 'string' &&
      url.includes('/api/v2/meta/bases') &&
      url.includes('/tables')
    ) {
      const baseId = url.match(/bases\/([^\/]+)\/tables/)?.[1];

      console.log('🚀 检测到表结构API请求', {
        url,
        baseId,
        timestamp,
        stack: new Error().stack,
      });

      requestLog.push({
        type: 'table_structure',
        url,
        baseId,
        timestamp,
        stack: new Error().stack,
      });
    }

    return originalFetch.apply(this, args);
  };

  // 提供调试工具函数
  window.debugApiRequests = {
    // 获取请求日志
    getRequestLog: () => {
      return requestLog;
    },

    // 获取表结构请求统计
    getTableStructureStats: () => {
      const tableRequests = requestLog.filter(
        (req) => req.type === 'table_structure'
      );
      const byBaseId = {};

      tableRequests.forEach((req) => {
        if (!byBaseId[req.baseId]) {
          byBaseId[req.baseId] = [];
        }
        byBaseId[req.baseId].push(req);
      });

      return {
        totalRequests: tableRequests.length,
        byBaseId,
        duplicates: Object.entries(byBaseId).filter(
          ([_, requests]) => requests.length > 1
        ),
      };
    },

    // 清理日志
    clearLog: () => {
      requestLog.length = 0;
      console.log('🧹 请求日志已清理');
    },

    // 获取缓存状态（如果可用）
    getCacheStatus: async () => {
      try {
        const results = {};

        // 尝试获取全局缓存状态
        if (window.GlobalTableStructureCache) {
          results.tableStructureCache =
            window.GlobalTableStructureCache.getCacheStats();
        }

        // 尝试获取请求去重器状态
        if (window.GlobalRequestDeduplicator) {
          results.requestDeduplicator =
            window.GlobalRequestDeduplicator.getStats();
        }

        // 尝试获取预加载器状态
        if (window.ApiPreloader) {
          results.preloader = window.ApiPreloader.getStats();
        }

        return results;
      } catch (error) {
        console.error('获取缓存状态失败:', error);
        return { error: error.message };
      }
    },

    // 生成测试报告
    generateReport: () => {
      const stats = window.debugApiRequests.getTableStructureStats();
      const report = {
        timestamp: new Date().toISOString(),
        summary: {
          totalTableStructureRequests: stats.totalRequests,
          uniqueBaseIds: Object.keys(stats.byBaseId).length,
          duplicateRequests: stats.duplicates.length,
        },
        details: stats.byBaseId,
        duplicates: stats.duplicates.map(([baseId, requests]) => ({
          baseId,
          requestCount: requests.length,
          timestamps: requests.map((r) => new Date(r.timestamp).toISOString()),
          timeDifferences: requests
            .slice(1)
            .map((r, i) => r.timestamp - requests[i].timestamp),
        })),
        recommendations: [],
      };

      // 生成建议
      if (stats.duplicates.length === 0) {
        report.recommendations.push(
          '✅ 没有检测到重复的表结构请求，修复成功！'
        );
      } else {
        report.recommendations.push('❌ 检测到重复请求，需要进一步优化');
        stats.duplicates.forEach(([baseId, requests]) => {
          report.recommendations.push(
            `- BaseId ${baseId}: ${requests.length}次请求`
          );
        });
      }

      console.log('📊 API请求测试报告:', report);
      return report;
    },

    // 开始监控特定时间段
    startMonitoring: (duration = 30000) => {
      console.log(`🔍 开始监控API请求 (${duration / 1000}秒)`);
      window.debugApiRequests.clearLog();

      setTimeout(() => {
        console.log('⏰ 监控时间结束，生成报告:');
        window.debugApiRequests.generateReport();
      }, duration);
    },
  };

  // 提供快捷命令
  console.log(`
🛠️ API调试工具已就绪！可用命令：

debugApiRequests.getRequestLog()        - 获取所有请求日志
debugApiRequests.getTableStructureStats() - 获取表结构请求统计
debugApiRequests.generateReport()      - 生成测试报告
debugApiRequests.startMonitoring(30000) - 开始30秒监控
debugApiRequests.clearLog()            - 清理日志
debugApiRequests.getCacheStatus()      - 获取缓存状态

示例用法：
debugApiRequests.startMonitoring(30000); // 开始30秒监控
// 然后进行你的测试操作（切换到争分夺秒环节等）
// 30秒后会自动生成报告
    `);
})();
